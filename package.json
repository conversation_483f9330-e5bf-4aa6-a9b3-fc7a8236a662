{"name": "bitnova-signer", "private": true, "type": "module", "scripts": {"build": "react-router build", "cf-typegen": "wrangler types && react-router typegen", "check": "tsc && react-router build && wrangler deploy --dry-run", "deploy": "wrangler deploy", "dev": "react-router dev", "preview": "npm run build && vite preview", "typecheck": "npm run typegen && tsc -b"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@rainbow-me/rainbowkit": "^2.2.8", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-query": "^5.81.5", "@wagmi/core": "^2.17.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "ethers": "^6.15.0", "isbot": "^5.1.27", "ky": "^1.8.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3", "siwe": "^3.0.0", "tailwind-merge": "^3.3.1", "viem": "2.x", "wagmi": "^2.15.6"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.9.0", "@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}
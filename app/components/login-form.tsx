import { GalleryVerticalEnd } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ConnectButton, useConnectModal } from '@rainbow-me/rainbowkit';
import { useAccount } from 'wagmi';
import { useCallback, useEffect } from 'react';
import { SiweMessage } from 'siwe';

export function LoginForm({ className, ...props }: React.ComponentProps<'div'>) {
  const { openConnectModal } = useConnectModal();
  const { address } = useAccount();

  const handleConnectWallet = () => {
    openConnectModal?.();
  };

  const handleSignIn = useCallback(async (address: string) => {
    const nonce = await fetch('/api/nonce', {
      headers: {
        'Content-Type': 'application/json',
      },
      method: 'GET',
    }).then((res) => res.json());

    const siweMessage = new SiweMessage({
      domain: window.location.host,
      address,
      statement: 'Sign in to BitNova Signer',
      uri: window.location.origin,
      version: '1',
      chainId: 1,
      nonce,
    });

    const signature = await signMessage(siweMessage.prepareMessage());

    await fetch('/api/sign-in', {
      body: JSON.stringify({
        message: siweMessage.prepareMessage(),
        signature,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
      method: 'POST',
    });
  }, []);

  useEffect(() => {
    if (!address) {
      return;
    }

    handleSignIn(address);
  }, [address]);

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-2">
          <a href="#" className="flex flex-col items-center gap-2 font-medium">
            <div className="flex size-8 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-6" />
            </div>
            <span className="sr-only">BitNova Signer</span>
          </a>
          <h1 className="text-xl font-bold">Welcome to BitNova Signer</h1>
          <p className="text-muted-foreground text-center text-sm">
            Connect your wallet to get started
          </p>
        </div>
        <div className="flex flex-col gap-6">
          <Button className="w-full cursor-pointer" onClick={handleConnectWallet}>
            Connect Wallet
          </Button>
        </div>
      </div>
      <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        By clicking continue, you agree to our <a href="#">Terms of Service</a> and{' '}
        <a href="#">Privacy Policy</a>.
      </div>
    </div>
  );
}

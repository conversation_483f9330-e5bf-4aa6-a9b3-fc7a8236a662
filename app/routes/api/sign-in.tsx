import { data, type ActionFunctionArgs } from 'react-router';
import { JsonRpcProvider } from 'ethers';
import { SiweErrorType, SiweMessage } from 'siwe';
import { commitSession, getSession } from '@/session.server';

type SignRequestBody = {
  ens: string;
  message: string;
  signature: string;
};

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request.headers.get('Cookie'));
  const body = await request.json<SignRequestBody>();

  const provider = new JsonRpcProvider('https://testnet-rpc.bitlayer.org');
  const message = new SiweMessage(body.message);

  try {
    const { data: fields } = await message.verify(
      {
        signature: body.signature.substring(2),
        nonce: session.get('nonce'),
      },
      { provider },
    );

    session.set('address', fields.address);

    return data(fields, {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    });
  } catch (error) {
    console.error(error);
    switch (error) {
      case SiweErrorType.EXPIRED_MESSAGE:
        return data(error, { status: 400 });
      case SiweErrorType.INVALID_SIGNATURE:
        return data(error, { status: 401 });
      case SiweErrorType.INVALID_NONCE:
        return data(error, { status: 401 });
      default:
        return data(error, { status: 400 });
    }
  }
}

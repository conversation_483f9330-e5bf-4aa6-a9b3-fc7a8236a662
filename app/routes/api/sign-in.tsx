import { data, type ActionFunctionArgs } from 'react-router';
import { JsonRpcProvider } from 'ethers';
import { SiweMessage } from 'siwe';
import { getSession } from '@/session.server';

type SignRequestBody = {
  message: string;
  signature: string;
};

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request.headers.get('<PERSON>ie'));
  const body = await request.json<SignRequestBody>();

  const provider = new JsonRpcProvider('https://testnet-rpc.bitlayer.org');
  const message = new SiweMessage(body.message);
  const { data: fields } = await message.verify(
    {
      signature: body.signature,
      nonce: session.get('nonce'),
    },
    { provider },
  );

  return data(fields);
}

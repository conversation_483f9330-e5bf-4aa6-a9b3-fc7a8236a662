import { commitSession, getSession } from '@/session.server';
import { data, type LoaderFunctionArgs } from 'react-router';
import { generateNonce } from 'siwe';

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request.headers.get('Cookie'));

  const nonce = generateNonce();
  session.set('nonce', nonce);

  return data(
    { nonce },
    {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    },
  );
}
